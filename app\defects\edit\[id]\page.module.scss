@import '../../../global';

.mainContainer {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100%;
    background-color: $offWhite;

    // Header
    .header {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        background-color: $white;
        min-height: 60px;
        border-bottom: $smallBorderWidth solid $primaryColor;

        .headerLabel {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
            color: $primaryColor;
        }
    }

    .contentContainer {
        display: flex;
        flex: 1;
        overflow: hidden;

        .leftPanel {
            width: 200px;
            min-width: 200px;
            background-color: $white;
            border-right: 1px solid $lightGrey;
            display: flex;
            flex-direction: column;

            .roomsHeader {
                background-color: $primaryColor;
                color: $white;
                padding: 10px 15px;
                font-weight: bold;
                font-size: 14px;
                text-align: center;
                border-bottom: 1px solid $lightGrey;
            }

            .roomsList {
                flex: 1;
                overflow-y: auto;

                .roomItem {
                    padding: 10px 15px;
                    cursor: pointer;
                    border-bottom: 1px solid $lightGrey;
                    font-size: 14px;
                    color: $offBlack;
                    background-color: $white;
                    transition: background-color 0.2s ease;

                    &:hover {
                        background-color: $offWhite;
                    }

                    &.selectedRoom {
                        background-color: $secondaryColor;
                        color: $white;
                        font-weight: bold;

                        &:hover {
                            background-color: $secondaryColor;
                        }
                    }
                }
            }
        }

        .rightPanel {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;

            .noRoomSelected {
                display: flex;
                align-items: center;
                justify-content: center;
                flex: 1;
                color: $darkGrey;
                font-style: italic;
                font-size: 16px;
            }

            .defectsContainer {
                flex: 1;
                padding: 20px;
                overflow-y: auto;

                .defectsTable {
                    width: 100%;

                    // Override JC_List styles for better defects display
                    :global(.defectsListOverride) {
                        font-size: 13px;

                        th {
                            background-color: $lightGrey;
                            color: $offBlack;
                            font-weight: 600;
                            padding: 12px 8px;
                            border-bottom: 2px solid $primaryColor;
                        }

                        td {
                            padding: 10px 8px;
                            border-bottom: 1px solid $lightGrey;
                            vertical-align: top;

                            &.defectsColumn {
                                max-width: 200px;
                                word-wrap: break-word;
                            }

                            &.imagesColumn {
                                text-align: center;
                                font-weight: bold;
                                color: $secondaryColor;
                            }
                        }

                        tr:hover {
                            background-color: $offWhite;
                        }
                    }
                }
            }

            .noDefects {
                display: flex;
                align-items: center;
                justify-content: center;
                flex: 1;
                color: $darkGrey;
                font-style: italic;
                font-size: 16px;
            }
        }
    }
}

// Responsive Design
@media (max-width: $tinyScreenSize) {
    .mainContainer {
        .contentContainer {
            flex-direction: column;

            .leftPanel {
                width: 100%;
                min-width: auto;
                height: 200px;
                border-right: none;
                border-bottom: 1px solid $lightGrey;
            }

            .rightPanel {
                flex: none;
                height: calc(100vh - 260px); // Adjust for header and left panel
            }
        }
    }
}
